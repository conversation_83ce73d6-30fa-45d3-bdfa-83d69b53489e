import React, { useState } from 'react';

function FolderDropZone({ folders, onPromptMoveToFolder, onFolderReorder }) {
  const [dragOverFolder, setDragOverFolder] = useState(null);
  const [draggedFolder, setDraggedFolder] = useState(null);

  const handleDragOver = (e, folder) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverFolder(folder);
  };

  const handleDragLeave = (e, folder) => {
    // 只有当鼠标真正离开文件夹区域时才清除高亮
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverFolder(null);
    }
  };

  const handleDrop = (e, folder) => {
    e.preventDefault();
    const promptId = e.dataTransfer.getData('text/plain');
    if (promptId && onPromptMoveToFolder) {
      onPromptMoveToFolder(promptId, folder ? folder.id : null);
    }
    setDragOverFolder(null);
  };

  return (
    <div className="folder-drop-zone">
      <div className="folder-drop-header">
        <h4>拖拽到文件夹</h4>
      </div>
      
      {/* 无文件夹选项 */}
      <div
        className={`folder-drop-item no-folder ${dragOverFolder === null ? 'drag-over' : ''}`}
        onDragOver={(e) => handleDragOver(e, null)}
        onDragLeave={(e) => handleDragLeave(e, null)}
        onDrop={(e) => handleDrop(e, null)}
      >
        <div className="folder-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-6l-2-2H5a2 2 0 0 0-2 2z"></path>
            <line x1="9" y1="12" x2="15" y2="12"></line>
          </svg>
        </div>
        <span className="folder-name">无文件夹</span>
      </div>

      {/* 文件夹列表 */}
      {folders.map(folder => (
        <div
          key={folder.id}
          className={`folder-drop-item ${dragOverFolder && dragOverFolder.id === folder.id ? 'drag-over' : ''}`}
          onDragOver={(e) => handleDragOver(e, folder)}
          onDragLeave={(e) => handleDragLeave(e, folder)}
          onDrop={(e) => handleDrop(e, folder)}
        >
          <div className="folder-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-6l-2-2H5a2 2 0 0 0-2 2z"></path>
            </svg>
          </div>
          <span className="folder-name">{folder.name}</span>
        </div>
      ))}
    </div>
  );
}

export default FolderDropZone;
