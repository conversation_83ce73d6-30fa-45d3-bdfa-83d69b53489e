(()=>{async function e(){try{return(await chrome.storage.local.get(["prompts"])).prompts||[]}catch(e){return console.error("Error getting prompts:",e),[]}}async function r(){try{return(await chrome.storage.local.get(["folders"])).folders||[]}catch(e){return console.error("Error getting folders:",e),[]}}function t(){return"id_"+Date.now()+"_"+Math.random().toString(36).substring(2,11)}chrome.commands.onCommand.addListener(e=>{"toggle-panel"===e&&chrome.tabs.query({active:!0,currentWindow:!0},e=>{e[0]&&chrome.tabs.sendMessage(e[0].id,{action:"toggle-panel"}).catch(()=>{})})}),chrome.runtime.onMessage.addListener((o,s,n)=>{switch(o.action){case"get-prompts":return e().then(n),!0;case"save-prompt":return async function(r){try{const o=await e();if(r.id){const e=o.findIndex(e=>e.id===r.id);-1!==e&&(o[e]={...r,updatedAt:Date.now()})}else r.id=t(),r.createdAt=Date.now(),r.updatedAt=Date.now(),r.usageCount=0,o.push(r);return await chrome.storage.local.set({prompts:o}),{success:!0,prompt:r}}catch(e){return console.error("Error saving prompt:",e),{success:!1,error:e.message}}}(o.prompt).then(n),!0;case"delete-prompt":return async function(r){try{const t=(await e()).filter(e=>e.id!==r);return await chrome.storage.local.set({prompts:t}),{success:!0}}catch(e){return console.error("Error deleting prompt:",e),{success:!1,error:e.message}}}(o.promptId).then(n),!0;case"get-folders":return r().then(n),!0;case"save-folder":return async function(e){try{const o=await r();if(e.id){const r=o.findIndex(r=>r.id===e.id);-1!==r&&(o[r]=e)}else e.id=t(),e.createdAt=Date.now(),e.order=o.length,o.push(e);return await chrome.storage.local.set({folders:o}),{success:!0,folder:e}}catch(e){return console.error("Error saving folder:",e),{success:!1,error:e.message}}}(o.folder).then(n),!0;case"delete-folder":return async function(e){try{const t=(await r()).filter(r=>r.id!==e);return await chrome.storage.local.set({folders:t}),{success:!0}}catch(e){return console.error("Error deleting folder:",e),{success:!1,error:e.message}}}(o.folderId).then(n),!0;case"export-data":return async function(){try{const t=await e(),o=await r();return{success:!0,data:{version:"1.0",exportTime:Date.now(),prompts:t,folders:o}}}catch(e){return console.error("Error exporting data:",e),{success:!1,error:e.message}}}().then(n),!0;case"import-data":return async function(e){try{if(!e.prompts||!Array.isArray(e.prompts))return{success:!1,error:"无效的数据格式：缺少prompts数组"};if(!e.folders||!Array.isArray(e.folders))return{success:!1,error:"无效的数据格式：缺少folders数组"};for(let r=0;r<e.prompts.length;r++){const o=e.prompts[r];if(!o.title||!o.content)return{success:!1,error:`第${r+1}个Prompt缺少必要字段（title或content）`};o.id||(o.id=t()),o.createdAt||(o.createdAt=Date.now()),o.updatedAt||(o.updatedAt=Date.now()),"number"!=typeof o.usageCount&&(o.usageCount=0)}for(let r=0;r<e.folders.length;r++){const o=e.folders[r];o.id||(o.id=t()),o.createdAt||(o.createdAt=Date.now()),"number"!=typeof o.order&&(o.order=r)}return await chrome.storage.local.set({prompts:e.prompts,folders:e.folders}),{success:!0,message:`成功导入 ${e.prompts.length} 个Prompt和 ${e.folders.length} 个文件夹`}}catch(e){return console.error("Error importing data:",e),{success:!1,error:e.message}}}(o.data).then(n),!0;case"move-prompt-to-folder":return async function(t,o){try{const s=await e(),n=s.findIndex(e=>e.id===t);if(-1===n)return{success:!1,error:"Prompt not found"};if(o){if(!(await r()).some(e=>e.id===o))return{success:!1,error:"Folder not found"}}return s[n].folderId=o,s[n].updatedAt=Date.now(),await chrome.storage.local.set({prompts:s}),{success:!0}}catch(e){return console.error("Error moving prompt to folder:",e),{success:!1,error:e.message}}}(o.promptId,o.folderId).then(n),!0}}),chrome.runtime.onInstalled.addListener(async()=>{const o=await e(),s=await r();if(0===o.length){const e=[{id:t(),title:"邮件开头",description:"专业的邮件问候语",content:"尊敬的{{姓名}}先生/女士：\n\n您好！",folderId:null,createdAt:Date.now(),updatedAt:Date.now(),usageCount:0},{id:t(),title:"会议邀请",description:"标准的会议邀请模板",content:"诚邀您参加我们将于{{时间}}举行的{{会议主题}}会议。\n\n会议地点：{{地点}}\n会议时间：{{时间}}\n\n期待您的参与！",folderId:null,createdAt:Date.now(),updatedAt:Date.now(),usageCount:0}];await chrome.storage.local.set({prompts:e})}if(0===s.length){const e=[{id:t(),name:"工作邮件",createdAt:Date.now(),order:0}];await chrome.storage.local.set({folders:e})}})})();