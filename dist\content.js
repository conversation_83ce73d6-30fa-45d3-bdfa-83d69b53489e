(()=>{let e=null,t=!1,n=null,o=null,i=!1,r=null,s=0;function c(){e||function(){if(e)return e;const t=document.createElement("div");t.id="prompt-manager-container",t.style.cssText="\n    position: fixed;\n    top: 0;\n    right: -400px;\n    width: 400px;\n    height: 100vh;\n    z-index: 2147483647;\n    transition: right 0.3s ease-in-out;\n    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\n    background: white;\n    border-left: 1px solid #e0e0e0;\n  ",e=document.createElement("iframe"),e.src=chrome.runtime.getURL("panel.html"),e.style.cssText="\n    width: 100%;\n    height: 100%;\n    border: none;\n    background: white;\n  ",t.appendChild(e),document.body.appendChild(t),window.addEventListener("message",l)}();const n=document.getElementById("prompt-manager-container");n&&(n.style.right="0px",t=!0,setTimeout(()=>{e&&e.contentWindow&&e.contentWindow.postMessage({action:"panel-shown",url:window.location.href},"*")},100))}function a(){const e=document.getElementById("prompt-manager-container");e&&(e.style.right="-400px",t=!1)}function l(t){var n,o;if(t.source===(null===(n=e)||void 0===n?void 0:n.contentWindow))switch(t.data.action){case"insert-prompt":!function(e){const t=d();if(t)try{if("true"===t.contentEditable){const t=window.getSelection(),n=t.getRangeAt(0);n.deleteContents(),n.insertNode(document.createTextNode(e)),n.collapse(!1),t.removeAllRanges(),t.addRange(n)}else{const n=t.selectionStart,o=t.selectionEnd,i=t.value,r=i.substring(0,n)+e+i.substring(o);t.value=r;const s=n+e.length;t.setSelectionRange(s,s)}t.dispatchEvent(new Event("input",{bubbles:!0})),t.focus(),a()}catch(e){console.error("Error inserting prompt:",e)}}(t.data.content);break;case"hide-panel":a();break;case"get-active-input":const n=d();e.contentWindow.postMessage({action:"active-input-info",hasActiveInput:!!n,inputType:null==n||null===(o=n.tagName)||void 0===o?void 0:o.toLowerCase()},"*")}}function d(){const e=document.activeElement;if(e&&("INPUT"===e.tagName||"TEXTAREA"===e.tagName||"true"===e.contentEditable))return e;const t=document.querySelectorAll('input[type="text"], input[type="email"], input[type="search"], textarea, [contenteditable="true"]');for(let e of t)if(null!==e.offsetParent)return e;return null}chrome.runtime.onMessage.addListener((e,n,o)=>{switch(e.action){case"toggle-panel":t?a():c(),o({success:!0});break;case"hide-panel":a(),o({success:!0})}}),document.addEventListener("mousemove",e=>{const o=window.innerWidth;e.clientX>=o-20?n||t||(n=setTimeout(()=>{c(),n=null},500)):n&&(clearTimeout(n),n=null)}),document.addEventListener("click",e=>{if(t){const t=document.getElementById("prompt-manager-container");t&&!t.contains(e.target)&&a()}i&&(o&&o.contains(e.target)||h())}),document.addEventListener("keydown",e=>{"Escape"===e.key&&t&&a()}),document.addEventListener("input",function(e){const t=e.target;if(!(n=t)||"INPUT"!==n.tagName&&"TEXTAREA"!==n.tagName&&"true"!==n.contentEditable)return;var n;const c=t.value||t.textContent||"",a=t.selectionStart||0;if("#"===c[a-1])s=a-1,r=t,async function(e){try{const t=await chrome.runtime.sendMessage({action:"get-prompts"});if(u=m(t||[],""),0===u.length)return void h();p=0,function(e){o||(o=document.createElement("div"),o.className="prompt-autocomplete-popup",o.style.cssText="\n    position: absolute;\n    background: white;\n    border: 1px solid #d1d5db;\n    border-radius: 6px;\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n    z-index: 2147483646;\n    max-width: 300px;\n    max-height: 200px;\n    overflow-y: auto;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n    font-size: 14px;\n  ",function(e){if(!o)return;const t=e.getBoundingClientRect(),n=window.pageYOffset||document.documentElement.scrollTop,i=window.pageXOffset||document.documentElement.scrollLeft;o.style.left=t.left+i+"px",o.style.top=t.bottom+n+2+"px"}(e),document.body.appendChild(o))}(e),g(),i=!0}catch(e){console.error("Error showing autocomplete:",e)}}(t);else if(r===t&&i){const e=s;a>e?(l=c.substring(e+1,a),i&&chrome.runtime.sendMessage({action:"get-prompts"},e=>{u=m(e||[],l),0!==u.length?(p=0,g()):h()})):h()}var l}),document.addEventListener("keydown",function(e){if(i)switch(e.key){case"ArrowDown":e.preventDefault(),0!==u.length&&(p=(p+1)%u.length,g());break;case"ArrowUp":e.preventDefault(),0!==u.length&&(p=p>0?p-1:u.length-1,g());break;case"Enter":e.preventDefault(),f();break;case"Escape":e.preventDefault(),h()}});let u=[],p=0;function m(e,t){if(!t)return e.slice(0,5);const n=t.toLowerCase();return e.filter(e=>e.title.toLowerCase().includes(n)||e.description&&e.description.toLowerCase().includes(n)).slice(0,5)}function g(){o&&(o.innerHTML="",u.forEach((e,t)=>{const n=document.createElement("div");n.className="autocomplete-item "+(t===p?"selected":""),n.style.cssText=`\n      padding: 8px 12px;\n      cursor: pointer;\n      border-bottom: 1px solid #f3f4f6;\n      ${t===p?"background: #eff6ff;":""}\n    `,n.innerHTML=`\n      <div style="font-weight: 600; color: #111827; margin-bottom: 2px;">${e.title}</div>\n      ${e.description?`<div style="font-size: 12px; color: #6b7280;">${e.description}</div>`:""}\n    `,n.addEventListener("click",()=>{p=t,f()}),o.appendChild(n)}))}function f(){if(!r||0===u.length)return;const e=u[p];if(e){try{const t=r,n=t.value||t.textContent||"",o=t.selectionStart||n.length,i=n.substring(0,s),c=n.substring(o),a=i+e.content+c;"true"===t.contentEditable?t.textContent=a:t.value=a;const l=i.length+e.content.length;t.setSelectionRange&&t.setSelectionRange(l,l),t.dispatchEvent(new Event("input",{bubbles:!0})),t.focus(),async function(e){try{const t=(await chrome.runtime.sendMessage({action:"get-prompts"})||[]).find(t=>t.id===e);if(t){const e={...t,usageCount:(t.usageCount||0)+1,lastUsedAt:Date.now()};await chrome.runtime.sendMessage({action:"save-prompt",prompt:e})}}catch(e){console.error("Error updating prompt usage:",e)}}(e.id)}catch(e){console.error("Error inserting autocomplete item:",e)}h()}}function h(){o&&(document.body.removeChild(o),o=null),i=!1,r=null,u=[],p=0}window.addEventListener("beforeunload",()=>{n&&clearTimeout(n),o&&document.body.removeChild(o)})})();