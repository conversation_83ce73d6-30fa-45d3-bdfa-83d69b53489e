import React, { useState, useEffect } from 'react';

function SettingsPanel({ onClose, onDataImported }) {
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'
  const [folders, setFolders] = useState([]);
  const [showFolderEditor, setShowFolderEditor] = useState(false);
  const [editingFolder, setEditingFolder] = useState(null);
  const [folderName, setFolderName] = useState('');

  // 初始化数据
  useEffect(() => {
    loadFolders();
  }, []);

  // 加载文件夹数据
  const loadFolders = async () => {
    try {
      const response = await sendMessageToBackground({ action: 'get-folders' });
      setFolders(response || []);
    } catch (error) {
      console.error('Error loading folders:', error);
    }
  };

  // 导出数据
  const handleExport = async () => {
    try {
      setExporting(true);
      setMessage('');
      
      const response = await sendMessageToBackground({ action: 'export-data' });
      
      if (response.success) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(response.data, null, 2)], {
          type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `prompt-manager-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        setMessage('数据导出成功！');
        setMessageType('success');
      } else {
        setMessage(`导出失败：${response.error}`);
        setMessageType('error');
      }
    } catch (error) {
      console.error('Export error:', error);
      setMessage(`导出失败：${error.message}`);
      setMessageType('error');
    } finally {
      setExporting(false);
    }
  };

  // 导入数据
  const handleImport = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        setImporting(true);
        setMessage('');
        
        const data = JSON.parse(e.target.result);
        const response = await sendMessageToBackground({ 
          action: 'import-data', 
          data 
        });
        
        if (response.success) {
          setMessage(response.message || '数据导入成功！');
          setMessageType('success');
          
          // 通知父组件数据已更新
          if (onDataImported) {
            onDataImported();
          }
        } else {
          setMessage(`导入失败：${response.error}`);
          setMessageType('error');
        }
      } catch (error) {
        console.error('Import error:', error);
        setMessage(`导入失败：${error.message}`);
        setMessageType('error');
      } finally {
        setImporting(false);
        // 清空文件输入
        event.target.value = '';
      }
    };
    
    reader.readAsText(file);
  };

  // 向background script发送消息
  const sendMessageToBackground = (message) => {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  };

  // 清除消息
  const clearMessage = () => {
    setMessage('');
    setMessageType('');
  };

  // 显示添加文件夹编辑器
  const showAddFolderEditor = () => {
    setEditingFolder(null);
    setFolderName('');
    setShowFolderEditor(true);
  };

  // 显示编辑文件夹编辑器
  const showEditFolderEditor = (folder) => {
    setEditingFolder(folder);
    setFolderName(folder.name);
    setShowFolderEditor(true);
  };

  // 隐藏文件夹编辑器
  const hideFolderEditor = () => {
    setShowFolderEditor(false);
    setEditingFolder(null);
    setFolderName('');
  };

  // 保存文件夹
  const handleSaveFolder = async () => {
    if (!folderName.trim()) {
      setMessage('文件夹名称不能为空');
      setMessageType('error');
      return;
    }

    try {
      const folderData = {
        ...editingFolder,
        name: folderName.trim()
      };

      const response = await sendMessageToBackground({
        action: 'save-folder',
        folder: folderData
      });

      if (response.success) {
        setMessage(editingFolder ? '文件夹更新成功！' : '文件夹创建成功！');
        setMessageType('success');
        hideFolderEditor();
        await loadFolders();
      } else {
        setMessage(`保存失败：${response.error}`);
        setMessageType('error');
      }
    } catch (error) {
      console.error('Error saving folder:', error);
      setMessage(`保存失败：${error.message}`);
      setMessageType('error');
    }
  };

  // 删除文件夹
  const handleDeleteFolder = async (folder) => {
    if (!window.confirm(`确定要删除文件夹"${folder.name}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const response = await sendMessageToBackground({
        action: 'delete-folder',
        folderId: folder.id
      });

      if (response.success) {
        setMessage('文件夹删除成功！');
        setMessageType('success');
        await loadFolders();
      } else {
        setMessage(`删除失败：${response.error}`);
        setMessageType('error');
      }
    } catch (error) {
      console.error('Error deleting folder:', error);
      setMessage(`删除失败：${error.message}`);
      setMessageType('error');
    }
  };

  return (
    <div className="settings-panel">
      <div className="settings-header">
        <h2 className="settings-title">设置</h2>
        <button className="close-button" onClick={onClose}>×</button>
      </div>
      
      <div className="settings-content">
        <div className="settings-section">
          <h3 className="section-title">文件夹管理</h3>

          <div className="setting-item">
            <div className="setting-info">
              <h4>文件夹列表</h4>
              <p>管理您的 Prompt 文件夹</p>
            </div>
            <button
              className="setting-button add-folder-button"
              onClick={showAddFolderEditor}
            >
              添加文件夹
            </button>
          </div>

          <div className="folder-list">
            {folders.map(folder => (
              <div key={folder.id} className="folder-item">
                <div className="folder-info">
                  <span className="folder-name">{folder.name}</span>
                  <span className="folder-meta">
                    创建于 {new Date(folder.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <div className="folder-actions">
                  <button
                    className="folder-action-button edit-button"
                    onClick={() => showEditFolderEditor(folder)}
                    title="编辑文件夹"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                  </button>
                  <button
                    className="folder-action-button delete-button"
                    onClick={() => handleDeleteFolder(folder)}
                    title="删除文件夹"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="3,6 5,6 21,6"></polyline>
                      <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                    </svg>
                  </button>
                </div>
              </div>
            ))}
            {folders.length === 0 && (
              <div className="empty-state">
                <p>暂无文件夹，点击上方按钮添加第一个文件夹</p>
              </div>
            )}
          </div>
        </div>

        <div className="settings-section">
          <h3 className="section-title">数据管理</h3>
          
          <div className="setting-item">
            <div className="setting-info">
              <h4>导出数据</h4>
              <p>将所有 Prompt 和文件夹导出为 JSON 文件</p>
            </div>
            <button 
              className="setting-button export-button"
              onClick={handleExport}
              disabled={exporting}
            >
              {exporting ? '导出中...' : '导出'}
            </button>
          </div>
          
          <div className="setting-item">
            <div className="setting-info">
              <h4>导入数据</h4>
              <p>从 JSON 文件导入 Prompt 和文件夹（会覆盖现有数据）</p>
            </div>
            <div className="import-controls">
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                disabled={importing}
                className="file-input"
                id="import-file"
              />
              <label 
                htmlFor="import-file" 
                className={`setting-button import-button ${importing ? 'disabled' : ''}`}
              >
                {importing ? '导入中...' : '选择文件'}
              </label>
            </div>
          </div>
        </div>
        
        {message && (
          <div className={`message ${messageType}`}>
            <span>{message}</span>
            <button className="message-close" onClick={clearMessage}>×</button>
          </div>
        )}
      </div>

      {showFolderEditor && (
        <div className="folder-editor-overlay">
          <div className="folder-editor">
            <div className="folder-editor-header">
              <h3>{editingFolder ? '编辑文件夹' : '添加文件夹'}</h3>
              <button className="close-button" onClick={hideFolderEditor}>×</button>
            </div>
            <div className="folder-editor-content">
              <div className="form-group">
                <label htmlFor="folder-name">文件夹名称</label>
                <input
                  id="folder-name"
                  type="text"
                  value={folderName}
                  onChange={(e) => setFolderName(e.target.value)}
                  placeholder="请输入文件夹名称"
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSaveFolder();
                    } else if (e.key === 'Escape') {
                      hideFolderEditor();
                    }
                  }}
                />
              </div>
            </div>
            <div className="folder-editor-actions">
              <button className="cancel-button" onClick={hideFolderEditor}>
                取消
              </button>
              <button className="save-button" onClick={handleSaveFolder}>
                {editingFolder ? '更新' : '创建'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default SettingsPanel;
