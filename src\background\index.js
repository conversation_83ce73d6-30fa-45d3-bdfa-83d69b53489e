// Background Script - Service Worker
// 处理快捷键、数据存储和消息路由

// 监听快捷键命令
chrome.commands.onCommand.addListener((command) => {
  
  if (command === 'toggle-panel') {
    // 向当前活动标签页发送消息，切换面板显示
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'toggle-panel'
        }).catch(() => {
          // Content script may not be ready yet
        });
      }
    });
  }
});

// 监听来自content script和panel的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  
  switch (request.action) {
    case 'get-prompts':
      getPrompts().then(sendResponse);
      return true; // 保持消息通道开放
      
    case 'save-prompt':
      savePrompt(request.prompt).then(sendResponse);
      return true;
      
    case 'delete-prompt':
      deletePrompt(request.promptId).then(sendResponse);
      return true;
      
    case 'get-folders':
      getFolders().then(sendResponse);
      return true;
      
    case 'save-folder':
      saveFolder(request.folder).then(sendResponse);
      return true;
      
    case 'delete-folder':
      deleteFolder(request.folderId).then(sendResponse);
      return true;

    case 'export-data':
      exportData().then(sendResponse);
      return true;

    case 'import-data':
      importData(request.data).then(sendResponse);
      return true;

    case 'move-prompt-to-folder':
      movePromptToFolder(request.promptId, request.folderId).then(sendResponse);
      return true;

    case 'reorder-prompts':
      reorderPrompts(request.promptIds).then(sendResponse);
      return true;

    case 'reorder-folders':
      reorderFolders(request.folderIds).then(sendResponse);
      return true;

    default:
      // Unknown action, ignore
  }
});

// 数据存储相关函数
async function getPrompts() {
  try {
    const result = await chrome.storage.local.get(['prompts']);
    return result.prompts || [];
  } catch (error) {
    console.error('Error getting prompts:', error);
    return [];
  }
}

async function savePrompt(prompt) {
  try {
    const prompts = await getPrompts();
    
    if (prompt.id) {
      // 更新现有prompt
      const index = prompts.findIndex(p => p.id === prompt.id);
      if (index !== -1) {
        prompts[index] = { ...prompt, updatedAt: Date.now() };
      }
    } else {
      // 添加新prompt
      prompt.id = generateId();
      prompt.createdAt = Date.now();
      prompt.updatedAt = Date.now();
      prompt.usageCount = 0;
      prompts.push(prompt);
    }
    
    await chrome.storage.local.set({ prompts });
    return { success: true, prompt };
  } catch (error) {
    console.error('Error saving prompt:', error);
    return { success: false, error: error.message };
  }
}

async function deletePrompt(promptId) {
  try {
    const prompts = await getPrompts();
    const filteredPrompts = prompts.filter(p => p.id !== promptId);
    await chrome.storage.local.set({ prompts: filteredPrompts });
    return { success: true };
  } catch (error) {
    console.error('Error deleting prompt:', error);
    return { success: false, error: error.message };
  }
}

async function getFolders() {
  try {
    const result = await chrome.storage.local.get(['folders']);
    return result.folders || [];
  } catch (error) {
    console.error('Error getting folders:', error);
    return [];
  }
}

async function saveFolder(folder) {
  try {
    const folders = await getFolders();
    
    if (folder.id) {
      // 更新现有文件夹
      const index = folders.findIndex(f => f.id === folder.id);
      if (index !== -1) {
        folders[index] = folder;
      }
    } else {
      // 添加新文件夹
      folder.id = generateId();
      folder.createdAt = Date.now();
      folder.order = folders.length;
      folders.push(folder);
    }
    
    await chrome.storage.local.set({ folders });
    return { success: true, folder };
  } catch (error) {
    console.error('Error saving folder:', error);
    return { success: false, error: error.message };
  }
}

async function deleteFolder(folderId) {
  try {
    const folders = await getFolders();
    const filteredFolders = folders.filter(f => f.id !== folderId);
    await chrome.storage.local.set({ folders: filteredFolders });
    return { success: true };
  } catch (error) {
    console.error('Error deleting folder:', error);
    return { success: false, error: error.message };
  }
}

// 导出数据
async function exportData() {
  try {
    const prompts = await getPrompts();
    const folders = await getFolders();

    const data = {
      version: '1.0',
      exportTime: Date.now(),
      prompts,
      folders
    };

    return { success: true, data };
  } catch (error) {
    console.error('Error exporting data:', error);
    return { success: false, error: error.message };
  }
}

// 导入数据
async function importData(importedData) {
  try {
    // 验证数据格式
    if (!importedData.prompts || !Array.isArray(importedData.prompts)) {
      return { success: false, error: '无效的数据格式：缺少prompts数组' };
    }

    if (!importedData.folders || !Array.isArray(importedData.folders)) {
      return { success: false, error: '无效的数据格式：缺少folders数组' };
    }

    // 验证每个prompt的必要字段
    for (let i = 0; i < importedData.prompts.length; i++) {
      const prompt = importedData.prompts[i];
      if (!prompt.title || !prompt.content) {
        return {
          success: false,
          error: `第${i + 1}个Prompt缺少必要字段（title或content）`
        };
      }

      // 确保每个prompt都有必要的字段
      if (!prompt.id) {
        prompt.id = generateId();
      }
      if (!prompt.createdAt) {
        prompt.createdAt = Date.now();
      }
      if (!prompt.updatedAt) {
        prompt.updatedAt = Date.now();
      }
      if (typeof prompt.usageCount !== 'number') {
        prompt.usageCount = 0;
      }
    }

    // 确保每个folder都有必要的字段
    for (let i = 0; i < importedData.folders.length; i++) {
      const folder = importedData.folders[i];
      if (!folder.id) {
        folder.id = generateId();
      }
      if (!folder.createdAt) {
        folder.createdAt = Date.now();
      }
      if (typeof folder.order !== 'number') {
        folder.order = i;
      }
    }

    // 保存导入的数据
    await chrome.storage.local.set({
      prompts: importedData.prompts,
      folders: importedData.folders
    });

    return {
      success: true,
      message: `成功导入 ${importedData.prompts.length} 个Prompt和 ${importedData.folders.length} 个文件夹`
    };

  } catch (error) {
    console.error('Error importing data:', error);
    return { success: false, error: error.message };
  }
}

// 移动Prompt到指定文件夹
async function movePromptToFolder(promptId, folderId) {
  try {
    const prompts = await getPrompts();
    const promptIndex = prompts.findIndex(p => p.id === promptId);

    if (promptIndex === -1) {
      return { success: false, error: 'Prompt not found' };
    }

    // 如果指定了文件夹ID，验证文件夹是否存在
    if (folderId) {
      const folders = await getFolders();
      const folderExists = folders.some(f => f.id === folderId);
      if (!folderExists) {
        return { success: false, error: 'Folder not found' };
      }
    }

    // 更新Prompt的文件夹ID
    prompts[promptIndex].folderId = folderId;
    prompts[promptIndex].updatedAt = Date.now();

    await chrome.storage.local.set({ prompts });
    return { success: true };
  } catch (error) {
    console.error('Error moving prompt to folder:', error);
    return { success: false, error: error.message };
  }
}

// 重新排序Prompts
async function reorderPrompts(promptIds) {
  try {
    const prompts = await getPrompts();

    // 为每个prompt设置新的order值
    promptIds.forEach((promptId, index) => {
      const promptIndex = prompts.findIndex(p => p.id === promptId);
      if (promptIndex !== -1) {
        prompts[promptIndex].order = index;
        prompts[promptIndex].updatedAt = Date.now();
      }
    });

    await chrome.storage.local.set({ prompts });
    return { success: true };
  } catch (error) {
    console.error('Error reordering prompts:', error);
    return { success: false, error: error.message };
  }
}

// 重新排序文件夹
async function reorderFolders(folderIds) {
  try {
    const folders = await getFolders();

    // 为每个文件夹设置新的order值
    folderIds.forEach((folderId, index) => {
      const folderIndex = folders.findIndex(f => f.id === folderId);
      if (folderIndex !== -1) {
        folders[folderIndex].order = index;
      }
    });

    await chrome.storage.local.set({ folders });
    return { success: true };
  } catch (error) {
    console.error('Error reordering folders:', error);
    return { success: false, error: error.message };
  }
}

// 生成唯一ID
function generateId() {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// 初始化默认数据
chrome.runtime.onInstalled.addListener(async () => {
  
  const prompts = await getPrompts();
  const folders = await getFolders();
  
  // 如果没有数据，创建一些示例数据
  if (prompts.length === 0) {
    const defaultPrompts = [
      {
        id: generateId(),
        title: '邮件开头',
        description: '专业的邮件问候语',
        content: '尊敬的{{姓名}}先生/女士：\n\n您好！',
        folderId: null,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        usageCount: 0
      },
      {
        id: generateId(),
        title: '会议邀请',
        description: '标准的会议邀请模板',
        content: '诚邀您参加我们将于{{时间}}举行的{{会议主题}}会议。\n\n会议地点：{{地点}}\n会议时间：{{时间}}\n\n期待您的参与！',
        folderId: null,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        usageCount: 0
      }
    ];
    
    await chrome.storage.local.set({ prompts: defaultPrompts });
  }
  
  if (folders.length === 0) {
    const defaultFolders = [
      {
        id: generateId(),
        name: '工作邮件',
        createdAt: Date.now(),
        order: 0
      }
    ];
    
    await chrome.storage.local.set({ folders: defaultFolders });
  }
});
